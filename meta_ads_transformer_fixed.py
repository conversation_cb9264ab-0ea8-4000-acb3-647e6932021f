
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
from datetime import datetime
import os
import traceback

class EnhancedAdsTransformer:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced Ads Data Transformer - Meta & Google Ads")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')

        # Data storage
        self.raw_data = None
        self.monthly_summary = None
        self.daily_aggregated = None
        self.data_quality_report = {}

        # Data source tracking
        self.current_data_source = None  # 'meta' or 'google'

        # Google Ads specific data
        self.gads_raw_data = None
        self.gads_monthly_summary = None
        self.gads_daily_aggregated = None
        self.gads_quality_report = {}

        self.setup_ui()
        
    def setup_ui(self):
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="Enhanced Ads Data Transformer",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))

        # Data Source Selection
        self.setup_data_source_panel(main_frame)

        # Control Panel
        self.setup_control_panel(main_frame)

        # Data Quality Panel
        self.setup_quality_panel(main_frame)

        # Data Preview Panel
        self.setup_preview_panel(main_frame)

        # Status Bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Select data source and load a CSV file to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var,
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

    def setup_data_source_panel(self, parent):
        # Data Source Selection Panel
        source_frame = ttk.LabelFrame(parent, text="Data Source", padding="10")
        source_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Radio buttons for data source selection
        self.data_source_var = tk.StringVar(value="meta")

        meta_radio = ttk.Radiobutton(source_frame, text="📊 Meta Ads (Facebook)",
                                    variable=self.data_source_var, value="meta",
                                    command=self.on_data_source_change)
        meta_radio.grid(row=0, column=0, padx=(0, 20), sticky=tk.W)

        google_radio = ttk.Radiobutton(source_frame, text="🔍 Google Ads",
                                      variable=self.data_source_var, value="google",
                                      command=self.on_data_source_change)
        google_radio.grid(row=0, column=1, sticky=tk.W)

        # Info label
        self.source_info_var = tk.StringVar()
        self.source_info_var.set("Meta Ads: Processes Facebook advertising campaign data")
        info_label = ttk.Label(source_frame, textvariable=self.source_info_var,
                              font=('Arial', 9), foreground='gray')
        info_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

    def on_data_source_change(self):
        """Handle data source selection change"""
        source = self.data_source_var.get()
        if source == "meta":
            self.source_info_var.set("Meta Ads: Processes Facebook advertising campaign data")
        else:
            self.source_info_var.set("Google Ads: Processes Google AdWords campaign data")

        # Clear existing data when switching sources
        self.clear_all_data()
        self.status_var.set(f"Switched to {source.title()} Ads - Load a CSV file to begin")

    def clear_all_data(self):
        """Clear all loaded data and displays"""
        self.raw_data = None
        self.monthly_summary = None
        self.daily_aggregated = None
        self.gads_raw_data = None
        self.gads_monthly_summary = None
        self.gads_daily_aggregated = None

        # Clear UI displays
        if hasattr(self, 'monthly_tree'):
            for item in self.monthly_tree.get_children():
                self.monthly_tree.delete(item)
        if hasattr(self, 'daily_tree'):
            for item in self.daily_tree.get_children():
                self.daily_tree.delete(item)
        if hasattr(self, 'raw_tree'):
            for item in self.raw_tree.get_children():
                self.raw_tree.delete(item)
        if hasattr(self, 'quality_text'):
            self.quality_text.config(state=tk.NORMAL)
            self.quality_text.delete(1.0, tk.END)
            self.quality_text.config(state=tk.DISABLED)

    def setup_control_panel(self, parent):
        # Control Panel Frame
        control_frame = ttk.LabelFrame(parent, text="Controls", padding="10")
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 10))
        
        # Load CSV Button
        load_btn = ttk.Button(control_frame, text="📁 Load CSV", 
                             command=self.load_csv, width=15)
        load_btn.grid(row=0, column=0, pady=5, sticky=tk.W)
        
        # Transform Options
        ttk.Label(control_frame, text="Transform Options:").grid(row=1, column=0, sticky=tk.W, pady=(10, 5))
        
        self.daily_var = tk.BooleanVar(value=True)
        self.monthly_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(control_frame, text="Daily Aggregation", 
                       variable=self.daily_var).grid(row=2, column=0, sticky=tk.W)
        ttk.Checkbutton(control_frame, text="Monthly Summary", 
                       variable=self.monthly_var).grid(row=3, column=0, sticky=tk.W)
        
        # Transform Button
        transform_btn = ttk.Button(control_frame, text="🔄 Transform Data", 
                                  command=self.transform_data, width=15)
        transform_btn.grid(row=4, column=0, pady=(10, 5), sticky=tk.W)
        
        # Export Buttons
        ttk.Label(control_frame, text="Export Options:").grid(row=5, column=0, sticky=tk.W, pady=(10, 5))
        
        export_daily_btn = ttk.Button(control_frame, text="💾 Export Daily", 
                                     command=self.export_daily, width=15)
        export_daily_btn.grid(row=6, column=0, pady=2, sticky=tk.W)
        
        export_monthly_btn = ttk.Button(control_frame, text="💾 Export Monthly", 
                                       command=self.export_monthly, width=15)
        export_monthly_btn.grid(row=7, column=0, pady=2, sticky=tk.W)
        
    def setup_quality_panel(self, parent):
        # Data Quality Panel
        quality_frame = ttk.LabelFrame(parent, text="Data Quality Report", padding="10")
        quality_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N), padx=(0, 10))
        quality_frame.columnconfigure(0, weight=1)
        
        # Quality metrics display
        self.quality_text = tk.Text(quality_frame, height=12, width=40, 
                                   font=('Courier', 9), state=tk.DISABLED)
        quality_scroll = ttk.Scrollbar(quality_frame, orient=tk.VERTICAL, 
                                      command=self.quality_text.yview)
        self.quality_text.configure(yscrollcommand=quality_scroll.set)
        
        self.quality_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        quality_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
    def setup_preview_panel(self, parent):
        # Data Preview Panel
        preview_frame = ttk.LabelFrame(parent, text="Data Preview", padding="10")
        preview_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(1, weight=1)
        
        # Notebook for different views
        self.notebook = ttk.Notebook(preview_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Monthly Summary Tab
        self.setup_monthly_tab()
        
        # Daily Data Tab
        self.setup_daily_tab()
        
        # Raw Data Tab
        self.setup_raw_tab()
        
    def setup_monthly_tab(self):
        monthly_frame = ttk.Frame(self.notebook)
        self.notebook.add(monthly_frame, text="Monthly Summary")
        
        # Monthly summary table
        columns = ('Month', 'Amount Spent (USD)', 'Results', 'Entry Count', 'Avg Frequency')
        self.monthly_tree = ttk.Treeview(monthly_frame, columns=columns, show='headings', height=10)
        
        # Configure columns
        for col in columns:
            self.monthly_tree.heading(col, text=col)
            self.monthly_tree.column(col, width=120, anchor=tk.CENTER)
        
        # Scrollbars
        monthly_scroll_y = ttk.Scrollbar(monthly_frame, orient=tk.VERTICAL, 
                                        command=self.monthly_tree.yview)
        monthly_scroll_x = ttk.Scrollbar(monthly_frame, orient=tk.HORIZONTAL, 
                                        command=self.monthly_tree.xview)
        self.monthly_tree.configure(yscrollcommand=monthly_scroll_y.set, 
                                   xscrollcommand=monthly_scroll_x.set)
        
        self.monthly_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        monthly_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        monthly_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        monthly_frame.columnconfigure(0, weight=1)
        monthly_frame.rowconfigure(0, weight=1)
        
    def setup_daily_tab(self):
        daily_frame = ttk.Frame(self.notebook)
        self.notebook.add(daily_frame, text="Daily Aggregated")
        
        # Daily data table
        columns = ('Date', 'Total Spend', 'Total Results', 'Total Reach', 'Total Impressions', 'Frequency')
        self.daily_tree = ttk.Treeview(daily_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.daily_tree.heading(col, text=col)
            self.daily_tree.column(col, width=100, anchor=tk.CENTER)
        
        daily_scroll_y = ttk.Scrollbar(daily_frame, orient=tk.VERTICAL, 
                                      command=self.daily_tree.yview)
        daily_scroll_x = ttk.Scrollbar(daily_frame, orient=tk.HORIZONTAL, 
                                      command=self.daily_tree.xview)
        self.daily_tree.configure(yscrollcommand=daily_scroll_y.set, 
                                 xscrollcommand=daily_scroll_x.set)
        
        self.daily_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        daily_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        daily_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        daily_frame.columnconfigure(0, weight=1)
        daily_frame.rowconfigure(0, weight=1)
        
    def setup_raw_tab(self):
        raw_frame = ttk.Frame(self.notebook)
        self.notebook.add(raw_frame, text="Raw Data Sample")
        
        # Raw data table (first 100 rows)
        columns = ('Date', 'Ad Name', 'Amount Spent', 'Results', 'Reach', 'Impressions')
        self.raw_tree = ttk.Treeview(raw_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.raw_tree.heading(col, text=col)
            self.raw_tree.column(col, width=120, anchor=tk.CENTER)
        
        raw_scroll_y = ttk.Scrollbar(raw_frame, orient=tk.VERTICAL, 
                                    command=self.raw_tree.yview)
        raw_scroll_x = ttk.Scrollbar(raw_frame, orient=tk.HORIZONTAL, 
                                    command=self.raw_tree.xview)
        self.raw_tree.configure(yscrollcommand=raw_scroll_y.set, 
                               xscrollcommand=raw_scroll_x.set)
        
        self.raw_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        raw_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        raw_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        raw_frame.columnconfigure(0, weight=1)
        raw_frame.rowconfigure(0, weight=1)
        
    def load_csv(self):
        try:
            data_source = self.data_source_var.get()
            title = f"Select {data_source.title()} Ads CSV File"

            file_path = filedialog.askopenfilename(
                title=title,
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if not file_path:
                return

            self.status_var.set("Loading CSV file...")
            self.root.update()

            # Load and validate data based on source
            if data_source == "meta":
                self.raw_data = self.load_and_validate_meta_csv(file_path)
                self.current_data_source = "meta"
                if self.raw_data is not None:
                    self.update_raw_data_display()
                    self.generate_quality_report()
                    self.status_var.set(f"Loaded {len(self.raw_data)} Meta Ads records successfully")
            else:  # Google Ads
                self.gads_raw_data = self.load_and_validate_gads_csv(file_path)
                self.current_data_source = "google"
                if self.gads_raw_data is not None:
                    self.update_gads_raw_data_display()
                    self.generate_gads_quality_report()
                    self.status_var.set(f"Loaded {len(self.gads_raw_data)} Google Ads records successfully")

            if (data_source == "meta" and self.raw_data is None) or \
               (data_source == "google" and self.gads_raw_data is None):
                self.status_var.set("Failed to load CSV file")

        except Exception as e:
            self.handle_error("Error loading CSV", e)
            
    def load_and_validate_meta_csv(self, file_path):
        try:
            # Load CSV
            df = pd.read_csv(file_path)

            # Skip summary row (first row)
            if len(df) > 1:
                df = df.iloc[1:].copy()

            # Clean column names
            df.columns = [c.strip() for c in df.columns]

            # All expected Meta Ads columns (21 total)
            expected_columns = [
                'Reporting starts', 'Reporting ends', 'Ad name', 'Ad delivery', 'Ad Set Name',
                'Bid', 'Bid type', 'Ad set budget', 'Ad set budget type', 'Last significant edit',
                'Attribution setting', 'Results', 'Result indicator', 'Reach', 'Impressions',
                'Cost per results', 'Quality ranking', 'Engagement rate ranking',
                'Conversion rate ranking', 'Amount spent (USD)', 'Ends'
            ]

            # Core required columns for basic functionality
            required_columns = ['Reporting ends', 'Amount spent (USD)', 'Results', 'Reach', 'Impressions']
            missing_required = [col for col in required_columns if col not in df.columns]

            if missing_required:
                messagebox.showerror("Missing Required Columns",
                                   f"Required Meta Ads columns missing: {', '.join(missing_required)}")
                return None

            # Check which optional columns are available
            available_columns = [col for col in expected_columns if col in df.columns]
            missing_optional = [col for col in expected_columns if col not in df.columns]

            print(f"Meta Ads: Available columns: {len(available_columns)}/{len(expected_columns)}")
            if missing_optional:
                print(f"Meta Ads: Missing optional columns: {missing_optional}")

            # Convert and validate data types
            df['Reporting ends'] = pd.to_datetime(df['Reporting ends'], errors='coerce')
            if 'Reporting starts' in df.columns:
                df['Reporting starts'] = pd.to_datetime(df['Reporting starts'], errors='coerce')
            if 'Last significant edit' in df.columns:
                df['Last significant edit'] = pd.to_datetime(df['Last significant edit'], errors='coerce')

            # Convert numeric columns
            numeric_columns = ['Amount spent (USD)', 'Results', 'Reach', 'Impressions', 'Bid',
                             'Ad set budget', 'Cost per results']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # Remove rows with invalid dates
            initial_count = len(df)
            df = df[df['Reporting ends'].notnull()].copy()

            if len(df) == 0:
                messagebox.showerror("Data Error", "No valid data found after cleaning")
                return None

            # Enhanced validation info with all available metrics
            self.data_quality_report = {
                'initial_rows': initial_count,
                'valid_rows': len(df),
                'date_range': (df['Reporting ends'].min(), df['Reporting ends'].max()),
                'total_spend': df['Amount spent (USD)'].sum(),
                'total_results': df['Results'].sum(),
                'total_reach': df['Reach'].sum(),
                'total_impressions': df['Impressions'].sum(),
                'available_columns': available_columns,
                'missing_columns': missing_optional,
                'unique_ads': df['Ad name'].nunique() if 'Ad name' in df.columns else 0,
                'unique_ad_sets': df['Ad Set Name'].nunique() if 'Ad Set Name' in df.columns else 0,
                'delivery_statuses': df['Ad delivery'].value_counts().to_dict() if 'Ad delivery' in df.columns else {},
                'bid_types': df['Bid type'].value_counts().to_dict() if 'Bid type' in df.columns else {},
                'attribution_settings': df['Attribution setting'].value_counts().to_dict() if 'Attribution setting' in df.columns else {}
            }

            return df

        except Exception as e:
            messagebox.showerror("File Error", f"Error reading Meta Ads CSV file: {str(e)}")
            return None

    def load_and_validate_gads_csv(self, file_path):
        """Load and validate Google Ads CSV file"""
        try:
            # Load CSV
            df = pd.read_csv(file_path)

            # Clean column names
            df.columns = [c.strip() for c in df.columns]

            # Validate required columns for Google Ads
            required_columns = ['Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks', 'Conversions']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messagebox.showerror("Missing Columns",
                                   f"Required Google Ads columns missing: {', '.join(missing_columns)}")
                return None

            # Convert and validate data types
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')

            # Convert numeric columns
            numeric_columns = ['Cost', 'Impressions', 'Clicks', 'Conversions', 'CTR', 'CPC', 'Conv. Rate', 'Cost per Conv.']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # Remove rows with invalid dates
            initial_count = len(df)
            df = df[df['Date'].notnull()].copy()

            if len(df) == 0:
                messagebox.showerror("Data Error", "No valid data found after cleaning")
                return None

            # Store validation info for Google Ads
            self.gads_quality_report = {
                'initial_rows': initial_count,
                'valid_rows': len(df),
                'date_range': (df['Date'].min(), df['Date'].max()),
                'total_cost': df['Cost'].sum(),
                'total_clicks': df['Clicks'].sum(),
                'total_impressions': df['Impressions'].sum(),
                'total_conversions': df['Conversions'].sum(),
                'unique_campaigns': df['Campaign ID'].nunique()
            }

            return df

        except Exception as e:
            messagebox.showerror("File Error", f"Error reading Google Ads CSV file: {str(e)}")
            return None

    def transform_data(self):
        # Check which data source is loaded
        if self.current_data_source == "meta" and self.raw_data is None:
            messagebox.showwarning("No Data", "Please load a Meta Ads CSV file first")
            return
        elif self.current_data_source == "google" and self.gads_raw_data is None:
            messagebox.showwarning("No Data", "Please load a Google Ads CSV file first")
            return
        elif self.current_data_source is None:
            messagebox.showwarning("No Data", "Please select a data source and load a CSV file first")
            return

        try:
            self.status_var.set("Transforming data...")
            self.root.update()

            if self.current_data_source == "meta":
                if self.monthly_var.get():
                    self.monthly_summary = self.aggregate_meta_by_period(self.raw_data, 'M')
                    self.update_monthly_display()

                if self.daily_var.get():
                    self.daily_aggregated = self.aggregate_meta_by_period(self.raw_data, 'D')
                    self.update_daily_display()
            else:  # Google Ads
                if self.monthly_var.get():
                    self.gads_monthly_summary = self.aggregate_gads_by_period(self.gads_raw_data, 'M')
                    self.update_gads_monthly_display()

                if self.daily_var.get():
                    self.gads_daily_aggregated = self.aggregate_gads_by_period(self.gads_raw_data, 'D')
                    self.update_gads_daily_display()

            self.status_var.set("Data transformation completed successfully")

        except Exception as e:
            self.handle_error("Error transforming data", e)
            
    def aggregate_meta_by_period(self, df, period):
        """Enhanced Meta Ads aggregation with comprehensive analytics"""
        try:
            # Build aggregation dictionary based on available columns
            agg_dict = {
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Reach': 'sum',
                'Impressions': 'sum',
                'Ad name': 'count'  # Count entries
            }

            # Add optional columns if available
            if 'Cost per results' in df.columns:
                agg_dict['Cost per results'] = 'mean'
            if 'Bid' in df.columns:
                agg_dict['Bid'] = 'mean'
            if 'Ad set budget' in df.columns:
                agg_dict['Ad set budget'] = 'sum'
            if 'Ad Set Name' in df.columns:
                agg_dict['Ad Set Name'] = 'nunique'  # Count unique ad sets

            grouped = df.groupby(df['Reporting ends'].dt.to_period(period)).agg(agg_dict).reset_index()

            # Rename columns
            rename_dict = {
                'Reporting ends': 'Period',
                'Amount spent (USD)': 'Total_Spend',
                'Results': 'Total_Results',
                'Reach': 'Total_Reach',
                'Impressions': 'Total_Impressions',
                'Ad name': 'Entry_Count'
            }

            if 'Cost per results' in grouped.columns:
                rename_dict['Cost per results'] = 'Avg_Cost_Per_Result'
            if 'Bid' in grouped.columns:
                rename_dict['Bid'] = 'Avg_Bid'
            if 'Ad set budget' in grouped.columns:
                rename_dict['Ad set budget'] = 'Total_Budget'
            if 'Ad Set Name' in grouped.columns:
                rename_dict['Ad Set Name'] = 'Unique_Ad_Sets'

            grouped = grouped.rename(columns=rename_dict)

            # Calculate derived metrics
            grouped['Frequency'] = np.where(
                grouped['Total_Reach'] > 0,
                grouped['Total_Impressions'] / grouped['Total_Reach'],
                0
            )

            grouped['Cost_Per_Result'] = np.where(
                grouped['Total_Results'] > 0,
                grouped['Total_Spend'] / grouped['Total_Results'],
                0
            )

            grouped['CPM'] = np.where(
                grouped['Total_Impressions'] > 0,
                (grouped['Total_Spend'] / grouped['Total_Impressions']) * 1000,
                0
            )

            # Format period
            grouped['Period'] = grouped['Period'].astype(str)

            return grouped

        except Exception as e:
            raise Exception(f"Error in Meta Ads aggregation: {str(e)}")

    def aggregate_gads_by_period(self, df, period):
        """Aggregate Google Ads data by time period"""
        try:
            # Group by period
            grouped = df.groupby(df['Date'].dt.to_period(period)).agg({
                'Cost': 'sum',
                'Impressions': 'sum',
                'Clicks': 'sum',
                'Conversions': 'sum',
                'Campaign ID': 'count'  # Count entries
            }).reset_index()

            # Rename columns
            grouped = grouped.rename(columns={
                'Date': 'Period',
                'Cost': 'Total_Cost',
                'Impressions': 'Total_Impressions',
                'Clicks': 'Total_Clicks',
                'Conversions': 'Total_Conversions',
                'Campaign ID': 'Entry_Count'
            })

            # Calculate derived metrics (handle division by zero)
            grouped['Avg_CTR'] = np.where(
                grouped['Total_Impressions'] > 0,
                (grouped['Total_Clicks'] / grouped['Total_Impressions']) * 100,
                0
            )

            grouped['Avg_CPC'] = np.where(
                grouped['Total_Clicks'] > 0,
                grouped['Total_Cost'] / grouped['Total_Clicks'],
                0
            )

            grouped['Conv_Rate'] = np.where(
                grouped['Total_Clicks'] > 0,
                (grouped['Total_Conversions'] / grouped['Total_Clicks']) * 100,
                0
            )

            grouped['Cost_Per_Conv'] = np.where(
                grouped['Total_Conversions'] > 0,
                grouped['Total_Cost'] / grouped['Total_Conversions'],
                0
            )

            # Format period
            grouped['Period'] = grouped['Period'].astype(str)

            return grouped

        except Exception as e:
            raise Exception(f"Error in Google Ads aggregation: {str(e)}")

    def update_monthly_display(self):
        # Clear existing data
        for item in self.monthly_tree.get_children():
            self.monthly_tree.delete(item)
            
        if self.monthly_summary is not None:
            for _, row in self.monthly_summary.iterrows():
                values = (
                    row['Period'],
                    f"${row['Total_Spend']:,.2f}",
                    f"{row['Total_Results']:,.0f}",
                    f"{row['Entry_Count']:,}",
                    f"{row['Frequency']:.2f}"
                )
                self.monthly_tree.insert('', 'end', values=values)
                
    def update_daily_display(self):
        # Clear existing data
        for item in self.daily_tree.get_children():
            self.daily_tree.delete(item)
            
        if self.daily_aggregated is not None:
            # Show first 50 rows to avoid overwhelming the display
            display_data = self.daily_aggregated.head(50)
            for _, row in display_data.iterrows():
                values = (
                    row['Period'],
                    f"${row['Total_Spend']:,.2f}",
                    f"{row['Total_Results']:,.0f}",
                    f"{row['Total_Reach']:,.0f}",
                    f"{row['Total_Impressions']:,.0f}",
                    f"{row['Frequency']:.2f}"
                )
                self.daily_tree.insert('', 'end', values=values)
                
    def update_raw_data_display(self):
        # Clear existing data
        for item in self.raw_tree.get_children():
            self.raw_tree.delete(item)
            
        if self.raw_data is not None:
            # Show first 50 rows
            display_data = self.raw_data.head(50)
            for _, row in display_data.iterrows():
                values = (
                    row['Reporting ends'].strftime('%Y-%m-%d') if pd.notnull(row['Reporting ends']) else 'Invalid',
                    str(row.get('Ad name', 'N/A'))[:20],  # Truncate long names
                    f"${row['Amount spent (USD)']:,.2f}" if pd.notnull(row['Amount spent (USD)']) else '$0.00',
                    f"{row['Results']:,.0f}" if pd.notnull(row['Results']) else '0',
                    f"{row['Reach']:,.0f}" if pd.notnull(row['Reach']) else '0',
                    f"{row['Impressions']:,.0f}" if pd.notnull(row['Impressions']) else '0'
                )
                self.raw_tree.insert('', 'end', values=values)

    def update_gads_monthly_display(self):
        """Update display for Google Ads monthly data"""
        # Clear existing data
        for item in self.monthly_tree.get_children():
            self.monthly_tree.delete(item)

        if self.gads_monthly_summary is not None:
            for _, row in self.gads_monthly_summary.iterrows():
                values = (
                    row['Period'],
                    f"${row['Total_Cost']:,.2f}",
                    f"{row['Total_Conversions']:,.0f}",
                    f"{row['Entry_Count']:,}",
                    f"{row['Avg_CTR']:.2f}%"
                )
                self.monthly_tree.insert('', 'end', values=values)

    def update_gads_daily_display(self):
        """Update display for Google Ads daily data"""
        # Clear existing data
        for item in self.daily_tree.get_children():
            self.daily_tree.delete(item)

        if self.gads_daily_aggregated is not None:
            # Show first 50 rows to avoid overwhelming the display
            display_data = self.gads_daily_aggregated.head(50)
            for _, row in display_data.iterrows():
                values = (
                    row['Period'],
                    f"${row['Total_Cost']:,.2f}",
                    f"{row['Total_Conversions']:,.0f}",
                    f"{row['Total_Clicks']:,.0f}",
                    f"{row['Total_Impressions']:,.0f}",
                    f"{row['Avg_CTR']:.2f}%"
                )
                self.daily_tree.insert('', 'end', values=values)

    def update_gads_raw_data_display(self):
        """Update display for Google Ads raw data"""
        # Clear existing data
        for item in self.raw_tree.get_children():
            self.raw_tree.delete(item)

        if self.gads_raw_data is not None:
            # Show first 50 rows
            display_data = self.gads_raw_data.head(50)
            for _, row in display_data.iterrows():
                values = (
                    row['Date'].strftime('%Y-%m-%d') if pd.notnull(row['Date']) else 'Invalid',
                    str(row.get('Campaign Name', 'N/A'))[:20],  # Truncate long names
                    f"${row['Cost']:,.2f}" if pd.notnull(row['Cost']) else '$0.00',
                    f"{row['Conversions']:,.0f}" if pd.notnull(row['Conversions']) else '0',
                    f"{row['Clicks']:,.0f}" if pd.notnull(row['Clicks']) else '0',
                    f"{row['Impressions']:,.0f}" if pd.notnull(row['Impressions']) else '0'
                )
                self.raw_tree.insert('', 'end', values=values)

    def generate_quality_report(self):
        if self.raw_data is None:
            return
            
        try:
            report = self.data_quality_report

            quality_text = f"""Meta Ads Data Quality Report
{'='*60}

Data Validation:
• Initial rows loaded: {report['initial_rows']:,}
• Valid rows after cleaning: {report['valid_rows']:,}
• Data quality: {(report['valid_rows']/report['initial_rows']*100):.1f}%
• Available columns: {len(report['available_columns'])}/21 total columns

Date Coverage:
• Start date: {report['date_range'][0].strftime('%Y-%m-%d')}
• End date: {report['date_range'][1].strftime('%Y-%m-%d')}
• Total days: {(report['date_range'][1] - report['date_range'][0]).days + 1}

Campaign Structure:
• Unique ads: {report['unique_ads']:,}
• Unique ad sets: {report['unique_ad_sets']:,}
• Total spend: ${report['total_spend']:,.2f}
• Total results: {report['total_results']:,.0f}
• Cost per result: ${(report['total_spend']/report['total_results']):.2f if report['total_results'] > 0 else 0:.2f}

Performance Metrics:
• Total reach: {report['total_reach']:,.0f}
• Total impressions: {report['total_impressions']:,.0f}
• Average frequency: {(report['total_impressions']/report['total_reach']):.2f if report['total_reach'] > 0 else 0:.2f}

Delivery Status Breakdown:
"""

            # Add delivery status breakdown
            if report['delivery_statuses']:
                for status, count in report['delivery_statuses'].items():
                    percentage = (count / report['valid_rows'] * 100)
                    quality_text += f"• {status}: {count:,} ads ({percentage:.1f}%)\n"
            else:
                quality_text += "• No delivery status data available\n"

            quality_text += f"""
Bid Strategy Analysis:
"""
            # Add bid type breakdown
            if report['bid_types']:
                for bid_type, count in report['bid_types'].items():
                    if bid_type and bid_type != '0':
                        percentage = (count / report['valid_rows'] * 100)
                        quality_text += f"• {bid_type}: {count:,} ads ({percentage:.1f}%)\n"
            else:
                quality_text += "• No bid type data available\n"

            quality_text += f"""
Attribution Settings:
"""
            # Add attribution breakdown
            if report['attribution_settings']:
                for attribution, count in report['attribution_settings'].items():
                    if attribution and attribution != '-' and attribution != 'Multiple attribution settings':
                        percentage = (count / report['valid_rows'] * 100)
                        quality_text += f"• {attribution}: {count:,} ads ({percentage:.1f}%)\n"
            else:
                quality_text += "• No attribution data available\n"

            # Display report
            self.quality_text.config(state=tk.NORMAL)
            self.quality_text.delete(1.0, tk.END)
            self.quality_text.insert(1.0, quality_text)
            self.quality_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.handle_error("Error generating quality report", e)

    def generate_gads_quality_report(self):
        """Generate quality report for Google Ads data"""
        if self.gads_raw_data is None:
            return

        try:
            report = self.gads_quality_report

            # Calculate additional metrics
            avg_ctr = (self.gads_raw_data['Clicks'].sum() / self.gads_raw_data['Impressions'].sum() * 100) if self.gads_raw_data['Impressions'].sum() > 0 else 0
            avg_cpc = (self.gads_raw_data['Cost'].sum() / self.gads_raw_data['Clicks'].sum()) if self.gads_raw_data['Clicks'].sum() > 0 else 0
            conv_rate = (self.gads_raw_data['Conversions'].sum() / self.gads_raw_data['Clicks'].sum() * 100) if self.gads_raw_data['Clicks'].sum() > 0 else 0
            cost_per_conv = (report['total_cost'] / report['total_conversions']) if report['total_conversions'] > 0 else 0

            quality_text = f"""Google Ads Data Quality Report
{'='*50}

Data Validation:
• Initial rows loaded: {report['initial_rows']:,}
• Valid rows after cleaning: {report['valid_rows']:,}
• Data quality: {(report['valid_rows']/report['initial_rows']*100):.1f}%

Date Coverage:
• Start date: {report['date_range'][0].strftime('%Y-%m-%d')}
• End date: {report['date_range'][1].strftime('%Y-%m-%d')}
• Total days: {(report['date_range'][1] - report['date_range'][0]).days + 1}

Campaign Summary:
• Unique campaigns: {report['unique_campaigns']:,}
• Total cost: ${report['total_cost']:,.2f}
• Total clicks: {report['total_clicks']:,.0f}
• Total impressions: {report['total_impressions']:,.0f}
• Total conversions: {report['total_conversions']:,.0f}

Performance Metrics:
• Average CTR: {avg_ctr:.2f}%
• Average CPC: ${avg_cpc:.2f}
• Conversion Rate: {conv_rate:.2f}%
• Cost per Conversion: ${cost_per_conv:.2f}

Top Campaigns by Conversions:
"""

            # Add top campaigns
            top_campaigns = self.gads_raw_data.groupby('Campaign Name').agg({
                'Conversions': 'sum',
                'Cost': 'sum',
                'Clicks': 'sum'
            }).sort_values('Conversions', ascending=False).head(5)

            for campaign, data in top_campaigns.iterrows():
                quality_text += f"• {campaign[:30]}: {data['Conversions']:.0f} conversions, ${data['Cost']:.2f} cost\n"

            # Update display
            self.quality_text.config(state=tk.NORMAL)
            self.quality_text.delete(1.0, tk.END)
            self.quality_text.insert(1.0, quality_text)
            self.quality_text.config(state=tk.DISABLED)

        except Exception as e:
            self.handle_error("Error generating Google Ads quality report", e)

    def analyze_meta_ads_performance(self):
        """Comprehensive Meta Ads performance analysis"""
        if self.raw_data is None:
            return None

        try:
            # Ad-level performance analysis
            ad_analysis = self.raw_data.groupby('Ad name').agg({
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Reach': 'sum',
                'Impressions': 'sum'
            }).reset_index()

            # Calculate performance metrics
            ad_analysis['Cost_Per_Result'] = np.where(
                ad_analysis['Results'] > 0,
                ad_analysis['Amount spent (USD)'] / ad_analysis['Results'],
                0
            )

            ad_analysis['Frequency'] = np.where(
                ad_analysis['Reach'] > 0,
                ad_analysis['Impressions'] / ad_analysis['Reach'],
                0
            )

            ad_analysis['CPM'] = np.where(
                ad_analysis['Impressions'] > 0,
                (ad_analysis['Amount spent (USD)'] / ad_analysis['Impressions']) * 1000,
                0
            )

            # Calculate efficiency score (results per dollar)
            ad_analysis['Efficiency_Score'] = np.where(
                ad_analysis['Amount spent (USD)'] > 0,
                ad_analysis['Results'] / ad_analysis['Amount spent (USD)'],
                0
            )

            # Sort by efficiency score
            ad_analysis = ad_analysis.sort_values('Efficiency_Score', ascending=False)

            return ad_analysis

        except Exception as e:
            self.handle_error("Error analyzing Meta Ads performance", e)
            return None

    def analyze_meta_ads_delivery_status(self):
        """Analyze Meta Ads delivery status and performance"""
        if self.raw_data is None or 'Ad delivery' not in self.raw_data.columns:
            return None

        try:
            # Delivery status analysis
            delivery_analysis = self.raw_data.groupby('Ad delivery').agg({
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Reach': 'sum',
                'Impressions': 'sum',
                'Ad name': 'count'
            }).reset_index()

            delivery_analysis = delivery_analysis.rename(columns={'Ad name': 'Ad_Count'})

            # Calculate metrics by delivery status
            delivery_analysis['Cost_Per_Result'] = np.where(
                delivery_analysis['Results'] > 0,
                delivery_analysis['Amount spent (USD)'] / delivery_analysis['Results'],
                0
            )

            delivery_analysis['Avg_Spend_Per_Ad'] = np.where(
                delivery_analysis['Ad_Count'] > 0,
                delivery_analysis['Amount spent (USD)'] / delivery_analysis['Ad_Count'],
                0
            )

            return delivery_analysis

        except Exception as e:
            self.handle_error("Error analyzing delivery status", e)
            return None

    def analyze_gads_campaign_performance(self):
        """Analyze Google Ads campaign performance and generate insights"""
        if self.gads_raw_data is None:
            return None

        try:
            # Campaign-level analysis
            campaign_analysis = self.gads_raw_data.groupby('Campaign Name').agg({
                'Cost': 'sum',
                'Clicks': 'sum',
                'Impressions': 'sum',
                'Conversions': 'sum'
            }).reset_index()

            # Calculate performance metrics
            campaign_analysis['CTR'] = np.where(
                campaign_analysis['Impressions'] > 0,
                (campaign_analysis['Clicks'] / campaign_analysis['Impressions']) * 100,
                0
            )

            campaign_analysis['CPC'] = np.where(
                campaign_analysis['Clicks'] > 0,
                campaign_analysis['Cost'] / campaign_analysis['Clicks'],
                0
            )

            campaign_analysis['Conv_Rate'] = np.where(
                campaign_analysis['Clicks'] > 0,
                (campaign_analysis['Conversions'] / campaign_analysis['Clicks']) * 100,
                0
            )

            campaign_analysis['Cost_Per_Conv'] = np.where(
                campaign_analysis['Conversions'] > 0,
                campaign_analysis['Cost'] / campaign_analysis['Conversions'],
                0
            )

            # Calculate ROI score (conversions per dollar spent)
            campaign_analysis['ROI_Score'] = np.where(
                campaign_analysis['Cost'] > 0,
                campaign_analysis['Conversions'] / campaign_analysis['Cost'],
                0
            )

            # Sort by ROI score
            campaign_analysis = campaign_analysis.sort_values('ROI_Score', ascending=False)

            return campaign_analysis

        except Exception as e:
            self.handle_error("Error analyzing campaign performance", e)
            return None

    def analyze_gads_cost_trends(self):
        """Analyze Google Ads cost trends over time"""
        if self.gads_raw_data is None:
            return None

        try:
            # Daily cost analysis
            daily_costs = self.gads_raw_data.groupby('Date').agg({
                'Cost': 'sum',
                'Clicks': 'sum',
                'Conversions': 'sum'
            }).reset_index()

            # Calculate moving averages
            daily_costs['Cost_7Day_Avg'] = daily_costs['Cost'].rolling(window=7, min_periods=1).mean()
            daily_costs['CPC_Daily'] = np.where(
                daily_costs['Clicks'] > 0,
                daily_costs['Cost'] / daily_costs['Clicks'],
                0
            )

            return daily_costs

        except Exception as e:
            self.handle_error("Error analyzing cost trends", e)
            return None

    def export_daily(self):
        if self.current_data_source == "meta":
            if self.daily_aggregated is None:
                messagebox.showwarning("No Data", "Please transform Meta Ads data first")
                return
            self.export_data(self.daily_aggregated, "daily_aggregated", "meta")
        else:  # Google Ads
            if self.gads_daily_aggregated is None:
                messagebox.showwarning("No Data", "Please transform Google Ads data first")
                return
            self.export_data(self.gads_daily_aggregated, "daily_aggregated", "google")

    def export_monthly(self):
        if self.current_data_source == "meta":
            if self.monthly_summary is None:
                messagebox.showwarning("No Data", "Please transform Meta Ads data first")
                return
            self.export_data(self.monthly_summary, "monthly_summary", "meta")
        else:  # Google Ads
            if self.gads_monthly_summary is None:
                messagebox.showwarning("No Data", "Please transform Google Ads data first")
                return
            self.export_data(self.gads_monthly_summary, "monthly_summary", "google")

    def export_data(self, data, default_name, source):
        try:
            source_prefix = "meta_ads" if source == "meta" else "google_ads"
            file_path = filedialog.asksaveasfilename(
                title=f"Save {source.title()} Ads {default_name.replace('_', ' ').title()}",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialfile=f"{source_prefix}_{default_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )

            if file_path:
                data.to_csv(file_path, index=False)
                messagebox.showinfo("Export Successful", f"Data exported to {file_path}")
                self.status_var.set(f"Exported {len(data)} {source.title()} Ads records to {os.path.basename(file_path)}")

        except Exception as e:
            self.handle_error("Error exporting data", e)
            
    def handle_error(self, title, error):
        error_msg = f"{title}: {str(error)}"
        print(f"ERROR: {error_msg}")
        print(traceback.format_exc())
        messagebox.showerror(title, error_msg)
        self.status_var.set(f"Error: {str(error)}")

def main():
    root = tk.Tk()
    app = EnhancedAdsTransformer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
